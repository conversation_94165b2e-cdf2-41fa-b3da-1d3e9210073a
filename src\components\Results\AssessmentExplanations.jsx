import { motion } from 'framer-motion';

const AssessmentRelation = ({ delay = 0 }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay }}
      className="mb-8"
    >
      <div className="bg-white rounded border border-gray-200 shadow-sm overflow-hidden">
        <div className="bg-gradient-to-r from-indigo-50 to-purple-50 border-b border-gray-200 p-6">
          <div className="flex items-center mb-4">
            <span className="text-3xl mr-3">🔗</span>
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Integrasi Tiga Dimensi Assessment</h2>
              <p className="text-indigo-700 font-medium">Pendekatan Holistik untuk Talent Mapping</p>
            </div>
          </div>
          <p className="text-gray-700 leading-relaxed mb-6">
            Sistem assessment kami mengintegrasikan tiga dimensi fundamental kepribadian manusia untuk memberikan
            analisis yang komprehensif dan akurat. Setiap assessment saling melengkapi dan memperkuat validitas
            hasil analisis secara keseluruhan.
          </p>

          {/* Three Pillars Visualization */}
          <div className="grid md:grid-cols-3 gap-6 mb-6">
            <div className="bg-white p-5 rounded-lg border border-indigo-100 text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-2xl">💪</span>
              </div>
              <h3 className="font-bold text-gray-900 mb-2">VIA Character Strengths</h3>
              <p className="text-sm text-gray-600 mb-3">Mengidentifikasi kekuatan karakter dan nilai-nilai inti</p>
              <div className="text-xs text-blue-700 bg-blue-50 px-2 py-1 rounded">
                Dimensi: Karakter & Nilai
              </div>
            </div>

            <div className="bg-white p-5 rounded-lg border border-indigo-100 text-center">
              <div className="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-2xl">🎯</span>
              </div>
              <h3 className="font-bold text-gray-900 mb-2">RIASEC Interests</h3>
              <p className="text-sm text-gray-600 mb-3">Menganalisis minat karier dan preferensi lingkungan kerja</p>
              <div className="text-xs text-emerald-700 bg-emerald-50 px-2 py-1 rounded">
                Dimensi: Minat & Motivasi
              </div>
            </div>

            <div className="bg-white p-5 rounded-lg border border-indigo-100 text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-2xl">🧠</span>
              </div>
              <h3 className="font-bold text-gray-900 mb-2">OCEAN Personality</h3>
              <p className="text-sm text-gray-600 mb-3">Mengukur trait kepribadian fundamental</p>
              <div className="text-xs text-purple-700 bg-purple-50 px-2 py-1 rounded">
                Dimensi: Kepribadian & Perilaku
              </div>
            </div>
          </div>

          {/* Integration Flow */}
          <div className="bg-white p-5 rounded-lg border border-indigo-100">
            <h4 className="font-semibold text-gray-900 mb-4 text-center">Bagaimana Ketiga Assessment Bekerja Sama</h4>
            <div className="space-y-4">
              <div className="flex items-start">
                <div className="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0 mt-1">
                  <span className="text-sm font-bold text-indigo-700">1</span>
                </div>
                <div>
                  <h5 className="font-medium text-gray-900">Triangulasi Data</h5>
                  <p className="text-sm text-gray-600">Setiap assessment memberikan perspektif unik yang saling memvalidasi dan memperkuat akurasi hasil analisis.</p>
                </div>
              </div>

              <div className="flex items-start">
                <div className="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0 mt-1">
                  <span className="text-sm font-bold text-indigo-700">2</span>
                </div>
                <div>
                  <h5 className="font-medium text-gray-900">Analisis Holistik</h5>
                  <p className="text-sm text-gray-600">Kombinasi karakter, minat, dan kepribadian memberikan gambaran lengkap tentang potensi dan preferensi karier Anda.</p>
                </div>
              </div>

              <div className="flex items-start">
                <div className="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0 mt-1">
                  <span className="text-sm font-bold text-indigo-700">3</span>
                </div>
                <div>
                  <h5 className="font-medium text-gray-900">Rekomendasi Terintegrasi</h5>
                  <p className="text-sm text-gray-600">Hasil akhir berupa persona karier yang menggabungkan insights dari ketiga dimensi untuk memberikan panduan yang akurat dan actionable.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default AssessmentRelation;